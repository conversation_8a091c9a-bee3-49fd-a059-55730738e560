import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Vibration,
  AppState,
  AppStateStatus,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { StackNavigationProp } from '@react-navigation/stack';
import { NavigationStackParamList, PomodoroState } from '../types';

type PomodoroScreenNavigationProp = StackNavigationProp<NavigationStackParamList, 'Pomodoro'>;

interface Props {
  navigation: PomodoroScreenNavigationProp;
}

const POMODORO_TIMES = {
  work: 25 * 60, // 25 minutes
  shortBreak: 5 * 60, // 5 minutes
  longBreak: 15 * 60, // 15 minutes
};

const STORAGE_KEY = 'pomodoro_state';

export default function PomodoroScreen({ navigation }: Props) {
  const [pomodoroState, setPomodoroState] = useState<PomodoroState>({
    isRunning: false,
    currentPhase: 'work',
    timeRemaining: POMODORO_TIMES.work,
    cycleCount: 0,
  });

  const [scaleAnim] = useState(new Animated.Value(1));
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const backgroundTimeRef = useRef<number>(0);

  // Load saved state on mount
  useEffect(() => {
    loadPomodoroState();
  }, []);

  // Save state whenever it changes
  useEffect(() => {
    savePomodoroState();
  }, [pomodoroState]);

  // Handle app state changes (background/foreground)
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background' && pomodoroState.isRunning) {
        // App going to background - save current time
        backgroundTimeRef.current = Date.now();
      } else if (nextAppState === 'active' && pomodoroState.isRunning && backgroundTimeRef.current > 0) {
        // App coming to foreground - calculate elapsed time
        const elapsedSeconds = Math.floor((Date.now() - backgroundTimeRef.current) / 1000);
        setPomodoroState(prev => {
          const newTimeRemaining = Math.max(0, prev.timeRemaining - elapsedSeconds);
          if (newTimeRemaining <= 0) {
            // Timer completed while in background
            setTimeout(() => handlePhaseComplete(), 100);
            return { ...prev, timeRemaining: 0 };
          }
          return { ...prev, timeRemaining: newTimeRemaining };
        });
        backgroundTimeRef.current = 0;
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [pomodoroState.isRunning]);

  useEffect(() => {
    if (pomodoroState.isRunning) {
      intervalRef.current = setInterval(() => {
        setPomodoroState(prev => {
          if (prev.timeRemaining <= 1) {
            // Time's up!
            handlePhaseComplete();
            return prev;
          }
          return { ...prev, timeRemaining: prev.timeRemaining - 1 };
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [pomodoroState.isRunning]);

  // Save pomodoro state to storage
  const savePomodoroState = async () => {
    try {
      const stateWithTimestamp = {
        ...pomodoroState,
        lastSaved: Date.now(),
      };
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(stateWithTimestamp));
    } catch (error) {
      console.error('Error saving pomodoro state:', error);
    }
  };

  // Load pomodoro state from storage
  const loadPomodoroState = async () => {
    try {
      const savedState = await AsyncStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        const { lastSaved, ...pomodoroData } = parsedState;

        // If timer was running when app was closed, calculate elapsed time
        if (pomodoroData.isRunning && lastSaved) {
          const elapsedSeconds = Math.floor((Date.now() - lastSaved) / 1000);
          const newTimeRemaining = Math.max(0, pomodoroData.timeRemaining - elapsedSeconds);

          if (newTimeRemaining <= 0) {
            // Timer completed while app was closed
            setPomodoroState({
              ...pomodoroData,
              isRunning: false,
              timeRemaining: 0,
            });
            setTimeout(() => handlePhaseComplete(), 500);
          } else {
            setPomodoroState({
              ...pomodoroData,
              timeRemaining: newTimeRemaining,
            });
          }
        } else {
          setPomodoroState(pomodoroData);
        }
      }
    } catch (error) {
      console.error('Error loading pomodoro state:', error);
    }
  };

  // Pulse animation when running
  useEffect(() => {
    if (pomodoroState.isRunning) {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
      return () => pulseAnimation.stop();
    } else {
      scaleAnim.setValue(1);
    }
  }, [pomodoroState.isRunning]);

  const handlePhaseComplete = () => {
    // Vibrate to notify user
    Vibration.vibrate([500, 200, 500]);

    const { currentPhase, cycleCount } = pomodoroState;
    let nextPhase: 'work' | 'shortBreak' | 'longBreak';
    let nextTime: number;
    let newCycleCount = cycleCount;

    if (currentPhase === 'work') {
      newCycleCount += 1;
      // After 4 work sessions, take a long break
      if (newCycleCount % 4 === 0) {
        nextPhase = 'longBreak';
        nextTime = POMODORO_TIMES.longBreak;
      } else {
        nextPhase = 'shortBreak';
        nextTime = POMODORO_TIMES.shortBreak;
      }
    } else {
      // After any break, go back to work
      nextPhase = 'work';
      nextTime = POMODORO_TIMES.work;
    }

    setPomodoroState({
      isRunning: false,
      currentPhase: nextPhase,
      timeRemaining: nextTime,
      cycleCount: newCycleCount,
    });

    // Show completion alert
    const phaseNames = {
      work: 'Çalışma',
      shortBreak: 'Kısa Mola',
      longBreak: 'Uzun Mola',
    };

    Alert.alert(
      `${phaseNames[currentPhase]} Tamamlandı! 🎉`,
      `Sıradaki: ${phaseNames[nextPhase]}`,
      [
        {
          text: 'Başla',
          onPress: () => startTimer(),
        },
        {
          text: 'Bekle',
          style: 'cancel',
        },
      ]
    );
  };

  const startTimer = () => {
    setPomodoroState(prev => ({ ...prev, isRunning: true }));
  };

  const pauseTimer = () => {
    setPomodoroState(prev => ({ ...prev, isRunning: false }));
  };

  const resetTimer = () => {
    Alert.alert(
      'Timer\'ı Sıfırla',
      'Timer\'ı sıfırlamak istediğinden emin misin?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sıfırla',
          style: 'destructive',
          onPress: async () => {
            const newState = {
              isRunning: false,
              currentPhase: 'work' as const,
              timeRemaining: POMODORO_TIMES.work,
              cycleCount: 0,
            };
            setPomodoroState(newState);
            // Clear saved state
            try {
              await AsyncStorage.removeItem(STORAGE_KEY);
            } catch (error) {
              console.error('Error clearing pomodoro state:', error);
            }
          },
        },
      ]
    );
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getPhaseInfo = () => {
    const { currentPhase } = pomodoroState;
    switch (currentPhase) {
      case 'work':
        return {
          title: 'Çalışma Zamanı',
          emoji: '📚',
          color: '#FF5722',
          description: 'Odaklan ve çalış!',
        };
      case 'shortBreak':
        return {
          title: 'Kısa Mola',
          emoji: '☕',
          color: '#4CAF50',
          description: 'Kısa bir mola ver',
        };
      case 'longBreak':
        return {
          title: 'Uzun Mola',
          emoji: '🛋️',
          color: '#2196F3',
          description: 'Uzun bir mola ver',
        };
    }
  };

  const phaseInfo = getPhaseInfo();
  const progress = 1 - (pomodoroState.timeRemaining / POMODORO_TIMES[pomodoroState.currentPhase]);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Pomodoro Timer</Text>
        <Text style={styles.cycleCount}>
          Tamamlanan Döngü: {Math.floor(pomodoroState.cycleCount / 2)}
        </Text>
      </View>

      {/* Timer Circle */}
      <View style={styles.timerContainer}>
        <Animated.View
          style={[
            styles.timerCircle,
            {
              borderColor: phaseInfo.color,
              transform: [{ scale: scaleAnim }]
            }
          ]}
        >
          <View style={styles.timerContent}>
            <Text style={styles.phaseEmoji}>{phaseInfo.emoji}</Text>
            <Text style={[styles.phaseTitle, { color: phaseInfo.color }]}>
              {phaseInfo.title}
            </Text>
            <Text style={styles.timeText}>
              {formatTime(pomodoroState.timeRemaining)}
            </Text>
            <Text style={styles.phaseDescription}>
              {phaseInfo.description}
            </Text>
          </View>
        </Animated.View>

        {/* Progress Ring */}
        <View style={styles.progressRing}>
          <View
            style={[
              styles.progressFill,
              {
                backgroundColor: phaseInfo.color,
                height: `${progress * 100}%`,
              }
            ]}
          />
        </View>
      </View>

      {/* Controls */}
      <View style={styles.controlsContainer}>
        {!pomodoroState.isRunning ? (
          <TouchableOpacity
            style={[styles.controlButton, styles.startButton]}
            onPress={startTimer}
          >
            <Text style={styles.controlButtonText}>▶️ Başla</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.controlButton, styles.pauseButton]}
            onPress={pauseTimer}
          >
            <Text style={styles.controlButtonText}>⏸️ Duraklat</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.controlButton, styles.resetButton]}
          onPress={resetTimer}
        >
          <Text style={styles.controlButtonText}>🔄 Sıfırla</Text>
        </TouchableOpacity>
      </View>

      {/* Info */}
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Pomodoro Tekniği</Text>
        <Text style={styles.infoText}>
          • 25 dakika çalış{'\n'}
          • 5 dakika mola ver{'\n'}
          • 4 döngü sonra 15 dakika uzun mola{'\n'}
          • Odaklanmayı artırır ve yorgunluğu azaltır
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    backgroundColor: '#FF9800',
    padding: 20,
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
  },
  cycleCount: {
    fontSize: 14,
    color: '#fff',
    marginTop: 5,
    opacity: 0.9,
  },
  timerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    position: 'relative',
  },
  timerCircle: {
    width: 280,
    height: 280,
    borderRadius: 140,
    borderWidth: 8,
    backgroundColor: '#fff',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 8,
  },
  timerContent: {
    alignItems: 'center',
  },
  phaseEmoji: {
    fontSize: 40,
    marginBottom: 10,
  },
  phaseTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  timeText: {
    fontSize: 48,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
    fontFamily: 'monospace',
  },
  phaseDescription: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
  progressRing: {
    position: 'absolute',
    width: 296,
    height: 296,
    borderRadius: 148,
    backgroundColor: 'transparent',
    borderWidth: 8,
    borderColor: '#e0e0e0',
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderRadius: 148,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 20,
    gap: 20,
  },
  controlButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 25,
    minWidth: 120,
    alignItems: 'center',
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  startButton: {
    backgroundColor: '#4CAF50',
  },
  pauseButton: {
    backgroundColor: '#FF9800',
  },
  resetButton: {
    backgroundColor: '#F44336',
  },
  controlButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  infoContainer: {
    backgroundColor: '#fff',
    margin: 20,
    padding: 20,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  infoTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  infoText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
});
