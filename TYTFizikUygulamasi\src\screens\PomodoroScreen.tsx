import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  Animated,
  Vibration,
  AppState,
  AppStateStatus,
} from 'react-native';
import AsyncStorage from '@react-native-async-storage/async-storage';
import * as Notifications from 'expo-notifications';
import { StackNavigationProp } from '@react-navigation/stack';
import { NavigationStackParamList, PomodoroState } from '../types';

// Configure notifications
Notifications.setNotificationHandler({
  handleNotification: async () => ({
    shouldShowAlert: true,
    shouldPlaySound: false,
    shouldSetBadge: false,
  }),
});

type PomodoroScreenNavigationProp = StackNavigationProp<NavigationStackParamList, 'Pomodoro'>;

interface Props {
  navigation: PomodoroScreenNavigationProp;
}

const POMODORO_TIMES = {
  work: 25 * 60, // 25 minutes
  shortBreak: 5 * 60, // 5 minutes
  longBreak: 15 * 60, // 15 minutes
};

const STORAGE_KEY = 'pomodoro_state';

export default function PomodoroScreen({ navigation }: Props) {
  const [pomodoroState, setPomodoroState] = useState<PomodoroState>({
    isRunning: false,
    currentPhase: 'work',
    timeRemaining: POMODORO_TIMES.work,
    cycleCount: 0,
  });

  const [scaleAnim] = useState(new Animated.Value(1));
  const intervalRef = useRef<NodeJS.Timeout | null>(null);
  const backgroundTimeRef = useRef<number>(0);
  const notificationId = useRef<string | null>(null);

  // Request notification permissions and load saved state on mount
  useEffect(() => {
    requestNotificationPermissions();
    loadPomodoroState();
  }, []);

  const requestNotificationPermissions = async () => {
    const { status } = await Notifications.requestPermissionsAsync();
    if (status !== 'granted') {
      Alert.alert(
        'Bildirim İzni',
        'Pomodoro timer\'ının düzgün çalışması için bildirim izni gereklidir.',
        [{ text: 'Tamam' }]
      );
    }
  };

  // Save state whenever it changes
  useEffect(() => {
    savePomodoroState();
  }, [pomodoroState]);

  // Handle app state changes (background/foreground)
  useEffect(() => {
    const handleAppStateChange = (nextAppState: AppStateStatus) => {
      if (nextAppState === 'background' && pomodoroState.isRunning) {
        // App going to background - save current time
        backgroundTimeRef.current = Date.now();
      } else if (nextAppState === 'active' && pomodoroState.isRunning && backgroundTimeRef.current > 0) {
        // App coming to foreground - calculate elapsed time
        const elapsedSeconds = Math.floor((Date.now() - backgroundTimeRef.current) / 1000);
        setPomodoroState(prev => {
          const newTimeRemaining = Math.max(0, prev.timeRemaining - elapsedSeconds);
          if (newTimeRemaining <= 0) {
            // Timer completed while in background
            setTimeout(() => handlePhaseComplete(), 100);
            return { ...prev, timeRemaining: 0 };
          }
          return { ...prev, timeRemaining: newTimeRemaining };
        });
        backgroundTimeRef.current = 0;
      }
    };

    const subscription = AppState.addEventListener('change', handleAppStateChange);
    return () => subscription?.remove();
  }, [pomodoroState.isRunning]);

  useEffect(() => {
    if (pomodoroState.isRunning) {
      intervalRef.current = setInterval(() => {
        setPomodoroState(prev => {
          if (prev.timeRemaining <= 1) {
            // Time's up!
            handlePhaseComplete();
            return prev;
          }
          return { ...prev, timeRemaining: prev.timeRemaining - 1 };
        });
      }, 1000);
    } else {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
        intervalRef.current = null;
      }
    }

    return () => {
      if (intervalRef.current) {
        clearInterval(intervalRef.current);
      }
    };
  }, [pomodoroState.isRunning]);

  // Update notification when timer changes
  useEffect(() => {
    if (pomodoroState.isRunning) {
      updateNotification();
    } else {
      clearNotification();
    }
  }, [pomodoroState.isRunning, pomodoroState.timeRemaining, pomodoroState.currentPhase]);

  const updateNotification = async () => {
    try {
      // Cancel previous notification
      if (notificationId.current) {
        await Notifications.cancelScheduledNotificationAsync(notificationId.current);
      }

      const phaseInfo = getPhaseInfo();
      const timeText = formatTime(pomodoroState.timeRemaining);

      // Schedule a persistent notification
      const id = await Notifications.scheduleNotificationAsync({
        content: {
          title: `🍅 ${phaseInfo.title}`,
          body: `⏰ ${timeText} kaldı - ${phaseInfo.description}`,
          data: {
            type: 'pomodoro',
            phase: pomodoroState.currentPhase,
            timeRemaining: pomodoroState.timeRemaining
          },
          sticky: true,
          priority: Notifications.AndroidNotificationPriority.HIGH,
        },
        trigger: null, // Show immediately
      });

      notificationId.current = id;
    } catch (error) {
      console.error('Error updating notification:', error);
    }
  };

  const clearNotification = async () => {
    try {
      if (notificationId.current) {
        await Notifications.cancelScheduledNotificationAsync(notificationId.current);
        notificationId.current = null;
      }
      // Clear all pomodoro notifications
      await Notifications.cancelAllScheduledNotificationsAsync();
    } catch (error) {
      console.error('Error clearing notification:', error);
    }
  };

  // Save pomodoro state to storage
  const savePomodoroState = async () => {
    try {
      const stateWithTimestamp = {
        ...pomodoroState,
        lastSaved: Date.now(),
      };
      await AsyncStorage.setItem(STORAGE_KEY, JSON.stringify(stateWithTimestamp));
    } catch (error) {
      console.error('Error saving pomodoro state:', error);
    }
  };

  // Load pomodoro state from storage
  const loadPomodoroState = async () => {
    try {
      const savedState = await AsyncStorage.getItem(STORAGE_KEY);
      if (savedState) {
        const parsedState = JSON.parse(savedState);
        const { lastSaved, ...pomodoroData } = parsedState;

        // If timer was running when app was closed, calculate elapsed time
        if (pomodoroData.isRunning && lastSaved) {
          const elapsedSeconds = Math.floor((Date.now() - lastSaved) / 1000);
          const newTimeRemaining = Math.max(0, pomodoroData.timeRemaining - elapsedSeconds);

          if (newTimeRemaining <= 0) {
            // Timer completed while app was closed
            setPomodoroState({
              ...pomodoroData,
              isRunning: false,
              timeRemaining: 0,
            });
            setTimeout(() => handlePhaseComplete(), 500);
          } else {
            setPomodoroState({
              ...pomodoroData,
              timeRemaining: newTimeRemaining,
            });
          }
        } else {
          setPomodoroState(pomodoroData);
        }
      }
    } catch (error) {
      console.error('Error loading pomodoro state:', error);
    }
  };

  // Pulse animation when running
  useEffect(() => {
    if (pomodoroState.isRunning) {
      const pulseAnimation = Animated.loop(
        Animated.sequence([
          Animated.timing(scaleAnim, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(scaleAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.start();
      return () => pulseAnimation.stop();
    } else {
      scaleAnim.setValue(1);
    }
  }, [pomodoroState.isRunning]);

  const handlePhaseComplete = () => {
    // Vibrate to notify user
    Vibration.vibrate([500, 200, 500]);

    const { currentPhase, cycleCount } = pomodoroState;
    let nextPhase: 'work' | 'shortBreak' | 'longBreak';
    let nextTime: number;
    let newCycleCount = cycleCount;

    if (currentPhase === 'work') {
      newCycleCount += 1;
      // After 4 work sessions, take a long break
      if (newCycleCount % 4 === 0) {
        nextPhase = 'longBreak';
        nextTime = POMODORO_TIMES.longBreak;
      } else {
        nextPhase = 'shortBreak';
        nextTime = POMODORO_TIMES.shortBreak;
      }
    } else {
      // After any break, go back to work
      nextPhase = 'work';
      nextTime = POMODORO_TIMES.work;
    }

    setPomodoroState({
      isRunning: false,
      currentPhase: nextPhase,
      timeRemaining: nextTime,
      cycleCount: newCycleCount,
    });

    // Show completion alert
    const phaseNames = {
      work: 'Çalışma',
      shortBreak: 'Kısa Mola',
      longBreak: 'Uzun Mola',
    };

    Alert.alert(
      `${phaseNames[currentPhase]} Tamamlandı! 🎉`,
      `Sıradaki: ${phaseNames[nextPhase]}`,
      [
        {
          text: 'Başla',
          onPress: () => startTimer(),
        },
        {
          text: 'Bekle',
          style: 'cancel',
        },
      ]
    );
  };

  const startTimer = () => {
    setPomodoroState(prev => ({ ...prev, isRunning: true }));
  };

  const pauseTimer = () => {
    setPomodoroState(prev => ({ ...prev, isRunning: false }));
    clearNotification();
  };

  const resetTimer = () => {
    Alert.alert(
      'Timer\'ı Sıfırla',
      'Timer\'ı sıfırlamak istediğinden emin misin?',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sıfırla',
          style: 'destructive',
          onPress: async () => {
            const newState = {
              isRunning: false,
              currentPhase: 'work' as const,
              timeRemaining: POMODORO_TIMES.work,
              cycleCount: 0,
            };
            setPomodoroState(newState);
            // Clear saved state
            try {
              await AsyncStorage.removeItem(STORAGE_KEY);
            } catch (error) {
              console.error('Error clearing pomodoro state:', error);
            }
          },
        },
      ]
    );
  };

  const formatTime = (seconds: number): string => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes.toString().padStart(2, '0')}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  const getPhaseInfo = () => {
    const { currentPhase } = pomodoroState;
    switch (currentPhase) {
      case 'work':
        return {
          title: 'Çalışma Zamanı',
          emoji: '📚',
          color: '#FF5722',
          description: 'Odaklan ve çalış!',
        };
      case 'shortBreak':
        return {
          title: 'Kısa Mola',
          emoji: '☕',
          color: '#4CAF50',
          description: 'Kısa bir mola ver',
        };
      case 'longBreak':
        return {
          title: 'Uzun Mola',
          emoji: '🛋️',
          color: '#2196F3',
          description: 'Uzun bir mola ver',
        };
    }
  };

  const phaseInfo = getPhaseInfo();
  const progress = 1 - (pomodoroState.timeRemaining / POMODORO_TIMES[pomodoroState.currentPhase]);

  return (
    <View style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.headerTitle}>Pomodoro Timer</Text>
        <Text style={styles.cycleCount}>
          Tamamlanan Döngü: {Math.floor(pomodoroState.cycleCount / 2)}
        </Text>
      </View>

      {/* Timer Circle */}
      <View style={styles.timerContainer}>
        <Animated.View
          style={[
            styles.timerCircle,
            {
              borderColor: phaseInfo.color,
              transform: [{ scale: scaleAnim }]
            }
          ]}
        >
          <View style={styles.timerContent}>
            <Text style={styles.phaseEmoji}>{phaseInfo.emoji}</Text>
            <Text style={[styles.phaseTitle, { color: phaseInfo.color }]}>
              {phaseInfo.title}
            </Text>
            <Text style={styles.timeText}>
              {formatTime(pomodoroState.timeRemaining)}
            </Text>
            <Text style={styles.phaseDescription}>
              {phaseInfo.description}
            </Text>
          </View>
        </Animated.View>

        {/* Progress Ring */}
        <View style={styles.progressRing}>
          <View
            style={[
              styles.progressFill,
              {
                backgroundColor: phaseInfo.color,
                height: `${progress * 100}%`,
              }
            ]}
          />
        </View>
      </View>

      {/* Controls */}
      <View style={styles.controlsContainer}>
        {!pomodoroState.isRunning ? (
          <TouchableOpacity
            style={[styles.controlButton, styles.startButton]}
            onPress={startTimer}
          >
            <Text style={styles.controlButtonText}>▶️ Başla</Text>
          </TouchableOpacity>
        ) : (
          <TouchableOpacity
            style={[styles.controlButton, styles.pauseButton]}
            onPress={pauseTimer}
          >
            <Text style={styles.controlButtonText}>⏸️ Duraklat</Text>
          </TouchableOpacity>
        )}

        <TouchableOpacity
          style={[styles.controlButton, styles.resetButton]}
          onPress={resetTimer}
        >
          <Text style={styles.controlButtonText}>🔄 Sıfırla</Text>
        </TouchableOpacity>
      </View>

      {/* Info */}
      <View style={styles.infoContainer}>
        <Text style={styles.infoTitle}>Pomodoro Tekniği</Text>
        <Text style={styles.infoText}>
          • 25 dakika çalış{'\n'}
          • 5 dakika mola ver{'\n'}
          • 4 döngü sonra 15 dakika uzun mola{'\n'}
          • Odaklanmayı artırır ve yorgunluğu azaltır
        </Text>
      </View>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#0f0f23',
  },
  header: {
    backgroundColor: '#1a1a2e',
    padding: 30,
    alignItems: 'center',
    borderBottomLeftRadius: 30,
    borderBottomRightRadius: 30,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.3,
    shadowRadius: 20,
    elevation: 15,
  },
  headerTitle: {
    fontSize: 28,
    fontWeight: '900',
    color: '#ffffff',
    letterSpacing: 1,
  },
  cycleCount: {
    fontSize: 16,
    color: '#8b8ba7',
    marginTop: 8,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  timerContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 40,
    position: 'relative',
  },
  timerCircle: {
    width: 320,
    height: 320,
    borderRadius: 160,
    borderWidth: 6,
    backgroundColor: '#16213e',
    justifyContent: 'center',
    alignItems: 'center',
    elevation: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 10 },
    shadowOpacity: 0.4,
    shadowRadius: 20,
  },
  timerContent: {
    alignItems: 'center',
  },
  phaseEmoji: {
    fontSize: 50,
    marginBottom: 15,
  },
  phaseTitle: {
    fontSize: 22,
    fontWeight: '900',
    marginBottom: 20,
    letterSpacing: 0.5,
  },
  timeText: {
    fontSize: 56,
    fontWeight: '900',
    color: '#ffffff',
    marginBottom: 15,
    fontFamily: 'monospace',
    letterSpacing: 2,
    textShadowColor: 'rgba(0,0,0,0.3)',
    textShadowOffset: { width: 0, height: 2 },
    textShadowRadius: 4,
  },
  phaseDescription: {
    fontSize: 16,
    color: '#8b8ba7',
    textAlign: 'center',
    fontWeight: '600',
    letterSpacing: 0.3,
  },
  progressRing: {
    position: 'absolute',
    width: 332,
    height: 332,
    borderRadius: 166,
    backgroundColor: 'transparent',
    borderWidth: 6,
    borderColor: 'rgba(139,139,167,0.2)',
    overflow: 'hidden',
  },
  progressFill: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    borderRadius: 166,
  },
  controlsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingHorizontal: 40,
    paddingVertical: 30,
    gap: 25,
  },
  controlButton: {
    paddingHorizontal: 35,
    paddingVertical: 18,
    borderRadius: 30,
    minWidth: 140,
    alignItems: 'center',
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 10,
    borderWidth: 1,
    borderColor: 'rgba(139,139,167,0.2)',
  },
  startButton: {
    backgroundColor: '#00ff88',
  },
  pauseButton: {
    backgroundColor: '#ff6b35',
  },
  resetButton: {
    backgroundColor: '#ff3838',
  },
  controlButtonText: {
    color: '#0f0f23',
    fontSize: 18,
    fontWeight: '900',
    letterSpacing: 0.5,
  },
  infoContainer: {
    backgroundColor: '#16213e',
    margin: 20,
    padding: 25,
    borderRadius: 25,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.3,
    shadowRadius: 12,
    borderWidth: 1,
    borderColor: 'rgba(139,139,167,0.2)',
  },
  infoTitle: {
    fontSize: 20,
    fontWeight: '900',
    color: '#ffffff',
    marginBottom: 15,
    letterSpacing: 0.5,
  },
  infoText: {
    fontSize: 16,
    color: '#8b8ba7',
    lineHeight: 26,
    fontWeight: '600',
    letterSpacing: 0.3,
  },
});
