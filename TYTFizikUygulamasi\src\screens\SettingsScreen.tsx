import React from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Linking,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { NavigationStackParamList } from '../types';
import { useApp } from '../context/AppContext';

type SettingsScreenNavigationProp = StackNavigationProp<NavigationStackParamList, 'Settings'>;

interface Props {
  navigation: SettingsScreenNavigationProp;
}

export default function SettingsScreen({ navigation }: Props) {
  const { state, resetProgress } = useApp();

  const handleResetProgress = () => {
    Alert.alert(
      '<PERSON>lerlemeyi Sıfırla',
      'Tüm ilerleme kaydedilecek ve baştan başlayacaksın. Bu işlem geri alınamaz!',
      [
        { text: 'İptal', style: 'cancel' },
        {
          text: 'Sıfırla',
          style: 'destructive',
          onPress: async () => {
            await resetProgress();
            Alert.alert('Ba<PERSON>arılı', '<PERSON><PERSON><PERSON><PERSON> sıfırlandı!');
            navigation.navigate('Home');
          },
        },
      ]
    );
  };

  const handleFeedback = () => {
    const email = '<EMAIL>';
    const subject = '14 Günde TYT Fiziği - Geri Bildirim';
    const body = 'Merhaba,\n\nUygulama hakkında geri bildirimim:\n\n';

    const mailtoUrl = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;

    Linking.canOpenURL(mailtoUrl).then(supported => {
      if (supported) {
        Linking.openURL(mailtoUrl);
      } else {
        Alert.alert('Hata', 'E-posta uygulaması bulunamadı.');
      }
    });
  };

  const handleAbout = () => {
    Alert.alert(
      'Hakkında',
      '14 Günde TYT Fiziği Bitir\n\nVersiyon: 1.0.0\n\nTYT Fizik sınavına hazırlık için tasarlanmış 14 günlük eğitim programı.\n\n© 2024 TYT Fizik Uygulaması',
      [{ text: 'Tamam' }]
    );
  };

  return (
    <ScrollView style={styles.container}>
      {/* User Progress Summary */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>İlerleme Özeti</Text>
        <View style={styles.progressCard}>
          <View style={styles.progressRow}>
            <Text style={styles.progressLabel}>Aktif Gün:</Text>
            <Text style={styles.progressValue}>{state.userProgress.active_day}</Text>
          </View>
          <View style={styles.progressRow}>
            <Text style={styles.progressLabel}>Tamamlanan Günler:</Text>
            <Text style={styles.progressValue}>
              {state.userProgress.completed_days.length}/14
            </Text>
          </View>
          <View style={styles.progressRow}>
            <Text style={styles.progressLabel}>Son Giriş:</Text>
            <Text style={styles.progressValue}>
              {state.userProgress.last_login_date}
            </Text>
          </View>
          <View style={styles.progressRow}>
            <Text style={styles.progressLabel}>Bugün Tamamlandı:</Text>
            <Text style={[
              styles.progressValue,
              { color: state.userProgress.today_completed ? '#4CAF50' : '#F44336' }
            ]}>
              {state.userProgress.today_completed ? 'Evet' : 'Hayır'}
            </Text>
          </View>
        </View>
      </View>

      {/* App Settings */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Uygulama Ayarları</Text>

        <TouchableOpacity style={styles.settingItem} onPress={handleResetProgress}>
          <View style={styles.settingContent}>
            <Text style={styles.settingIcon}>🔄</Text>
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>İlerlemeyi Sıfırla</Text>
              <Text style={styles.settingDescription}>
                Tüm ilerlemeyi sil ve baştan başla
              </Text>
            </View>
          </View>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>
      </View>

      {/* Support */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>Destek</Text>

        <TouchableOpacity style={styles.settingItem} onPress={handleFeedback}>
          <View style={styles.settingContent}>
            <Text style={styles.settingIcon}>📧</Text>
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>Geri Bildirim Gönder</Text>
              <Text style={styles.settingDescription}>
                Önerilerinizi ve sorunlarınızı bildirin
              </Text>
            </View>
          </View>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>

        <TouchableOpacity style={styles.settingItem} onPress={handleAbout}>
          <View style={styles.settingContent}>
            <Text style={styles.settingIcon}>ℹ️</Text>
            <View style={styles.settingText}>
              <Text style={styles.settingTitle}>Hakkında</Text>
              <Text style={styles.settingDescription}>
                Uygulama bilgileri ve versiyon
              </Text>
            </View>
          </View>
          <Text style={styles.settingArrow}>›</Text>
        </TouchableOpacity>
      </View>

      {/* Tips */}
      <View style={styles.section}>
        <Text style={styles.sectionTitle}>💡 İpuçları</Text>
        <View style={styles.tipsCard}>
          <Text style={styles.tipText}>
            • Her gün düzenli olarak çalış{'\n'}
            • Testleri geçmek için %70 başarı gerekli{'\n'}
            • Pomodoro tekniğini kullanarak odaklan{'\n'}
            • Dış kaynak sorularını çözmeyi unutma{'\n'}
            • Aynı gün içinde sadece 1 gün tamamlayabilirsin
          </Text>
        </View>
      </View>

      {/* Footer */}
      <View style={styles.footer}>
        <Text style={styles.footerText}>
          14 Günde TYT Fiziği Bitir
        </Text>
        <Text style={styles.footerSubText}>
          Başarılar dileriz! 🎯
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  section: {
    marginTop: 20,
    paddingHorizontal: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  progressCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  progressRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    paddingVertical: 8,
    borderBottomWidth: 1,
    borderBottomColor: '#f0f0f0',
  },
  progressLabel: {
    fontSize: 14,
    color: '#666',
  },
  progressValue: {
    fontSize: 14,
    fontWeight: 'bold',
    color: '#333',
  },
  settingItem: {
    backgroundColor: '#fff',
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 15,
    marginBottom: 10,
    borderRadius: 10,
    elevation: 1,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 1,
  },
  settingContent: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  settingIcon: {
    fontSize: 24,
    marginRight: 15,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 12,
    color: '#666',
  },
  settingArrow: {
    fontSize: 20,
    color: '#ccc',
    marginLeft: 10,
  },
  tipsCard: {
    backgroundColor: '#fff',
    padding: 20,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  tipText: {
    fontSize: 14,
    color: '#666',
    lineHeight: 22,
  },
  footer: {
    alignItems: 'center',
    padding: 30,
    marginTop: 20,
  },
  footerText: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#2196F3',
    marginBottom: 5,
  },
  footerSubText: {
    fontSize: 14,
    color: '#666',
  },
});
