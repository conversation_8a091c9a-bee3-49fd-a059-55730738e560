import React, { useState, useEffect } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Linking,
  Alert,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { RouteProp } from '@react-navigation/native';
import { NavigationStackParamList, DayContent } from '../types';
import { useApp } from '../context/AppContext';
import physicsData from '../data/physicsContent.json';

type DailyTaskScreenNavigationProp = StackNavigationProp<NavigationStackParamList, 'DailyTask'>;
type DailyTaskScreenRouteProp = RouteProp<NavigationStackParamList, 'DailyTask'>;

interface Props {
  navigation: DailyTaskScreenNavigationProp;
  route: DailyTaskScreenRouteProp;
}

export default function DailyTaskScreen({ navigation, route }: Props) {
  const { day } = route.params;
  const { state, canAccessDay } = useApp();
  const [dayContent, setDayContent] = useState<DayContent | null>(null);
  const [hasAccess, setHasAccess] = useState(false);

  useEffect(() => {
    const checkAccess = async () => {
      const access = await canAccessDay(day);
      setHasAccess(access);
      
      if (!access) {
        Alert.alert(
          'Erişim Engellendi',
          'Bu güne erişmek için önceki günü tamamlamalısın!',
          [
            {
              text: 'Geri Dön',
              onPress: () => navigation.goBack(),
            },
          ]
        );
        return;
      }

      // Load day content
      const content = physicsData.days.find(d => d.day === day);
      if (content) {
        setDayContent(content);
      }
    };

    checkAccess();
  }, [day, canAccessDay, navigation]);

  const handleVideoPress = async () => {
    if (!dayContent?.video_url) return;

    try {
      const supported = await Linking.canOpenURL(dayContent.video_url);
      if (supported) {
        await Linking.openURL(dayContent.video_url);
      } else {
        Alert.alert('Hata', 'Video açılamadı. Lütfen YouTube uygulamasının yüklü olduğundan emin olun.');
      }
    } catch (error) {
      Alert.alert('Hata', 'Video açılırken bir hata oluştu.');
    }
  };

  const handleStartTest = () => {
    if (!dayContent) return;

    // Check if user already completed today
    if (state.userProgress.today_completed && 
        state.userProgress.last_login_date === new Date().toDateString()) {
      Alert.alert(
        'Günlük Limit',
        'Bugün zaten bir gün tamamladın. Yarın tekrar dene!',
        [{ text: 'Tamam' }]
      );
      return;
    }

    navigation.navigate('Test', {
      day,
      questions: dayContent.test_questions,
    });
  };

  const handlePomodoroPress = () => {
    navigation.navigate('Pomodoro');
  };

  if (!hasAccess || !dayContent) {
    return (
      <View style={styles.loadingContainer}>
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  const isCompleted = state.userProgress.completed_days.includes(day);
  const canTakeTest = !state.userProgress.today_completed || 
                     state.userProgress.last_login_date !== new Date().toDateString();

  return (
    <ScrollView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <Text style={styles.dayTitle}>{day}. Gün</Text>
        <Text style={styles.topicTitle}>{dayContent.topic}</Text>
        {isCompleted && (
          <View style={styles.completedBadge}>
            <Text style={styles.completedText}>✅ Tamamlandı</Text>
          </View>
        )}
      </View>

      {/* Motivation */}
      <View style={styles.motivationCard}>
        <Text style={styles.motivationTitle}>Günün Motivasyonu</Text>
        <Text style={styles.motivationText}>{dayContent.motivation}</Text>
      </View>

      {/* Tasks */}
      <View style={styles.tasksContainer}>
        <Text style={styles.sectionTitle}>Günün Görevleri</Text>

        {/* Video Task */}
        <View style={styles.taskCard}>
          <View style={styles.taskHeader}>
            <Text style={styles.taskNumber}>1</Text>
            <Text style={styles.taskTitle}>Konu Anlatımı İzle</Text>
          </View>
          <Text style={styles.taskDescription}>
            {dayContent.topic} konusunun video anlatımını izle
          </Text>
          <TouchableOpacity style={styles.videoButton} onPress={handleVideoPress}>
            <Text style={styles.videoButtonText}>📺 Video İzle</Text>
          </TouchableOpacity>
        </View>

        {/* Summary Task */}
        <View style={styles.taskCard}>
          <View style={styles.taskHeader}>
            <Text style={styles.taskNumber}>2</Text>
            <Text style={styles.taskTitle}>Konu Özeti</Text>
          </View>
          <Text style={styles.summaryText}>{dayContent.summary_text}</Text>
        </View>

        {/* Test Task */}
        <View style={styles.taskCard}>
          <View style={styles.taskHeader}>
            <Text style={styles.taskNumber}>3</Text>
            <Text style={styles.taskTitle}>Test Çöz (30 Soru)</Text>
          </View>
          <Text style={styles.taskDescription}>
            Konuyu pekiştirmek için 30 soruluk testi çöz. Geçmek için minimum %70 başarı gerekli.
          </Text>
          <TouchableOpacity
            style={[
              styles.testButton,
              !canTakeTest && styles.testButtonDisabled
            ]}
            onPress={handleStartTest}
            disabled={!canTakeTest}
          >
            <Text style={styles.testButtonText}>
              {!canTakeTest ? 'Bugün Tamamlandı' : '📝 Teste Başla'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* External Task */}
        <View style={styles.taskCard}>
          <View style={styles.taskHeader}>
            <Text style={styles.taskNumber}>4</Text>
            <Text style={styles.taskTitle}>Dış Kaynak Çalışması</Text>
          </View>
          <Text style={styles.externalTaskText}>{dayContent.external_task}</Text>
          <Text style={styles.externalTaskNote}>
            💡 Bu görevi uygulama dışında tamamla
          </Text>
        </View>

        {/* Pomodoro Task */}
        <View style={styles.taskCard}>
          <View style={styles.taskHeader}>
            <Text style={styles.taskNumber}>5</Text>
            <Text style={styles.taskTitle}>Pomodoro Çalışması (Opsiyonel)</Text>
          </View>
          <Text style={styles.taskDescription}>
            Odaklanmak için Pomodoro tekniğini kullan
          </Text>
          <TouchableOpacity style={styles.pomodoroButton} onPress={handlePomodoroPress}>
            <Text style={styles.pomodoroButtonText}>🍅 Pomodoro Başlat</Text>
          </TouchableOpacity>
        </View>
      </View>

      {/* Progress Info */}
      <View style={styles.progressInfo}>
        <Text style={styles.progressTitle}>İlerleme Bilgisi</Text>
        <Text style={styles.progressText}>
          • Tamamlanan günler: {state.userProgress.completed_days.length}/14
        </Text>
        <Text style={styles.progressText}>
          • Aktif gün: {state.userProgress.active_day}
        </Text>
        {state.userProgress.today_completed && (
          <Text style={styles.todayCompletedText}>
            • Bugün bir gün tamamladın! ✅
          </Text>
        )}
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    alignItems: 'center',
  },
  dayTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    color: '#fff',
  },
  topicTitle: {
    fontSize: 18,
    color: '#fff',
    marginTop: 5,
    opacity: 0.9,
  },
  completedBadge: {
    backgroundColor: '#4CAF50',
    paddingHorizontal: 12,
    paddingVertical: 4,
    borderRadius: 12,
    marginTop: 10,
  },
  completedText: {
    color: '#fff',
    fontSize: 14,
    fontWeight: 'bold',
  },
  motivationCard: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  motivationTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  motivationText: {
    fontSize: 16,
    color: '#666',
    fontStyle: 'italic',
    textAlign: 'center',
    lineHeight: 22,
  },
  tasksContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  taskCard: {
    backgroundColor: '#fff',
    padding: 15,
    borderRadius: 10,
    marginBottom: 15,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  taskHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 10,
  },
  taskNumber: {
    backgroundColor: '#2196F3',
    color: '#fff',
    width: 24,
    height: 24,
    borderRadius: 12,
    textAlign: 'center',
    lineHeight: 24,
    fontSize: 14,
    fontWeight: 'bold',
    marginRight: 10,
  },
  taskTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    flex: 1,
  },
  taskDescription: {
    fontSize: 14,
    color: '#666',
    marginBottom: 15,
    lineHeight: 20,
  },
  videoButton: {
    backgroundColor: '#FF5722',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  videoButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  summaryText: {
    fontSize: 14,
    color: '#333',
    lineHeight: 22,
    backgroundColor: '#f8f9fa',
    padding: 15,
    borderRadius: 8,
    borderLeftWidth: 4,
    borderLeftColor: '#2196F3',
  },
  testButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  testButtonDisabled: {
    backgroundColor: '#ccc',
  },
  testButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  externalTaskText: {
    fontSize: 14,
    color: '#333',
    marginBottom: 10,
    lineHeight: 20,
  },
  externalTaskNote: {
    fontSize: 12,
    color: '#FF9800',
    fontStyle: 'italic',
  },
  pomodoroButton: {
    backgroundColor: '#FF9800',
    paddingVertical: 12,
    paddingHorizontal: 20,
    borderRadius: 8,
    alignItems: 'center',
  },
  pomodoroButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  progressInfo: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  progressTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 10,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    marginBottom: 5,
  },
  todayCompletedText: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
});
