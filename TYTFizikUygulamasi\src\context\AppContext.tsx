import React, { createContext, useContext, useReducer, useEffect, ReactNode } from 'react';
import { UserProgress, TestResult, AppAction } from '../types';
import StorageService from '../services/StorageService';

interface AppState {
  userProgress: UserProgress;
  loading: boolean;
  currentTestResult?: TestResult | null;
}

const initialState: AppState = {
  userProgress: {
    active_day: 1,
    completed_days: [],
    last_login_date: new Date().toDateString(),
    today_completed: false,
    test_attempts: {},
  },
  loading: true,
};

const AppContext = createContext<{
  state: AppState;
  dispatch: React.Dispatch<AppAction>;
  completeDay: (day: number, score: number) => Promise<boolean>;
  canAccessDay: (day: number) => Promise<boolean>;
  recordTestAttempt: (day: number) => Promise<void>;
  resetProgress: () => Promise<void>;
} | null>(null);

function appReducer(state: AppState, action: AppAction): AppState {
  switch (action.type) {
    case 'SET_LOADING':
      return { ...state, loading: action.payload };
    
    case 'SET_USER_PROGRESS':
      return { ...state, userProgress: action.payload, loading: false };
    
    case 'SET_TEST_RESULT':
      return { ...state, currentTestResult: action.payload };
    
    case 'COMPLETE_DAY':
      const { day, score } = action.payload;
      const updatedProgress = { ...state.userProgress };
      
      if (!updatedProgress.completed_days.includes(day)) {
        updatedProgress.completed_days.push(day);
      }
      
      updatedProgress.active_day = Math.min(day + 1, 14);
      updatedProgress.today_completed = true;
      updatedProgress.current_test_score = score;
      updatedProgress.last_login_date = new Date().toDateString();
      
      return { ...state, userProgress: updatedProgress };
    
    case 'RESET_PROGRESS':
      return { ...state, userProgress: initialState.userProgress };
    
    default:
      return state;
  }
}

export function AppProvider({ children }: { children: ReactNode }) {
  const [state, dispatch] = useReducer(appReducer, initialState);

  // Initialize app data
  useEffect(() => {
    const initializeApp = async () => {
      try {
        dispatch({ type: 'SET_LOADING', payload: true });
        
        // Check for daily reset
        await StorageService.checkDailyReset();
        
        // Load user progress
        const progress = await StorageService.getUserProgress();
        dispatch({ type: 'SET_USER_PROGRESS', payload: progress });
      } catch (error) {
        console.error('Error initializing app:', error);
        dispatch({ type: 'SET_LOADING', payload: false });
      }
    };

    initializeApp();
  }, []);

  const completeDay = async (day: number, score: number): Promise<boolean> => {
    const success = await StorageService.completeDay(day, score);
    if (success) {
      dispatch({ type: 'COMPLETE_DAY', payload: { day, score } });
    }
    return success;
  };

  const canAccessDay = async (day: number): Promise<boolean> => {
    return await StorageService.canAccessDay(day);
  };

  const recordTestAttempt = async (day: number): Promise<void> => {
    await StorageService.recordTestAttempt(day);
    // Refresh user progress to update attempts
    const progress = await StorageService.getUserProgress();
    dispatch({ type: 'SET_USER_PROGRESS', payload: progress });
  };

  const resetProgress = async (): Promise<void> => {
    await StorageService.resetProgress();
    dispatch({ type: 'RESET_PROGRESS' });
  };

  return (
    <AppContext.Provider
      value={{
        state,
        dispatch,
        completeDay,
        canAccessDay,
        recordTestAttempt,
        resetProgress,
      }}
    >
      {children}
    </AppContext.Provider>
  );
}

export function useApp() {
  const context = useContext(AppContext);
  if (!context) {
    throw new Error('useApp must be used within an AppProvider');
  }
  return context;
}
