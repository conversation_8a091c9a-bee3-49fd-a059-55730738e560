import AsyncStorage from '@react-native-async-storage/async-storage';
import { UserProgress } from '../types';

const STORAGE_KEYS = {
  USER_PROGRESS: 'user_progress',
  LAST_LOGIN_DATE: 'last_login_date',
  TODAY_COMPLETED: 'today_completed',
};

export class StorageService {
  // Initialize default user progress
  private static getDefaultProgress(): UserProgress {
    return {
      active_day: 1,
      completed_days: [],
      last_login_date: new Date().toDateString(),
      today_completed: false,
      test_attempts: {},
    };
  }

  // Get user progress from storage
  static async getUserProgress(): Promise<UserProgress> {
    try {
      const progressData = await AsyncStorage.getItem(STORAGE_KEYS.USER_PROGRESS);
      if (progressData) {
        return JSON.parse(progressData);
      }
      return this.getDefaultProgress();
    } catch (error) {
      console.error('Error getting user progress:', error);
      return this.getDefaultProgress();
    }
  }

  // Save user progress to storage
  static async saveUserProgress(progress: UserProgress): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.USER_PROGRESS, JSON.stringify(progress));
    } catch (error) {
      console.error('Error saving user progress:', error);
    }
  }

  // Check if user can access a specific day
  static async canAccessDay(day: number): Promise<boolean> {
    const progress = await this.getUserProgress();
    
    // Day 1 is always accessible
    if (day === 1) return true;
    
    // Can access if previous day is completed
    return progress.completed_days.includes(day - 1);
  }

  // Complete a day
  static async completeDay(day: number, score: number): Promise<boolean> {
    try {
      const progress = await this.getUserProgress();
      const today = new Date().toDateString();
      
      // Check if user already completed a day today
      if (progress.today_completed && progress.last_login_date === today) {
        return false; // Cannot complete another day on the same day
      }
      
      // Add day to completed days if not already there
      if (!progress.completed_days.includes(day)) {
        progress.completed_days.push(day);
      }
      
      // Update progress
      progress.active_day = Math.min(day + 1, 14);
      progress.last_login_date = today;
      progress.today_completed = true;
      progress.current_test_score = score;
      
      await this.saveUserProgress(progress);
      return true;
    } catch (error) {
      console.error('Error completing day:', error);
      return false;
    }
  }

  // Check if today's completion is available
  static async checkDailyReset(): Promise<void> {
    try {
      const progress = await this.getUserProgress();
      const today = new Date().toDateString();
      
      // Reset today_completed if it's a new day
      if (progress.last_login_date !== today) {
        progress.today_completed = false;
        progress.last_login_date = today;
        await this.saveUserProgress(progress);
      }
    } catch (error) {
      console.error('Error checking daily reset:', error);
    }
  }

  // Record test attempt
  static async recordTestAttempt(day: number): Promise<void> {
    try {
      const progress = await this.getUserProgress();
      progress.test_attempts[day] = (progress.test_attempts[day] || 0) + 1;
      await this.saveUserProgress(progress);
    } catch (error) {
      console.error('Error recording test attempt:', error);
    }
  }

  // Get test attempts for a day
  static async getTestAttempts(day: number): Promise<number> {
    try {
      const progress = await this.getUserProgress();
      return progress.test_attempts[day] || 0;
    } catch (error) {
      console.error('Error getting test attempts:', error);
      return 0;
    }
  }

  // Reset all progress (for testing purposes)
  static async resetProgress(): Promise<void> {
    try {
      await AsyncStorage.removeItem(STORAGE_KEYS.USER_PROGRESS);
    } catch (error) {
      console.error('Error resetting progress:', error);
    }
  }

  // Get previous days summary for review
  static async getPreviousDaysSummary(currentDay: number): Promise<number[]> {
    const progress = await this.getUserProgress();
    const previousDays = [];
    
    // Get last 3 completed days
    for (let i = Math.max(1, currentDay - 3); i < currentDay; i++) {
      if (progress.completed_days.includes(i)) {
        previousDays.push(i);
      }
    }
    
    return previousDays.slice(-3); // Return last 3 days
  }
}
