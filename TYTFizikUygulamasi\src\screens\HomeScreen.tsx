import React, { useEffect, useState } from 'react';
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  ActivityIndicator,
} from 'react-native';
import { StackNavigationProp } from '@react-navigation/stack';
import { NavigationStackParamList } from '../types';
import { useApp } from '../context/AppContext';
import physicsData from '../data/physicsContent.json';

type HomeScreenNavigationProp = StackNavigationProp<NavigationStackParamList, 'Home'>;

interface Props {
  navigation: HomeScreenNavigationProp;
}

export default function HomeScreen({ navigation }: Props) {
  const { state, canAccessDay } = useApp();
  const [accessibleDays, setAccessibleDays] = useState<boolean[]>([]);

  useEffect(() => {
    const checkDayAccess = async () => {
      const access = [];
      for (let i = 1; i <= 14; i++) {
        const canAccess = await canAccessDay(i);
        access.push(canAccess);
      }
      setAccessibleDays(access);
    };

    if (!state.loading) {
      checkDayAccess();
    }
  }, [state.loading, state.userProgress, canAccessDay]);

  const handleDayPress = async (day: number) => {
    const canAccess = await canAccessDay(day);
    
    if (!canAccess) {
      Alert.alert(
        'Gün Kilitli',
        'Bu güne erişmek için önceki günü tamamlamalısın!',
        [{ text: 'Tamam' }]
      );
      return;
    }

    navigation.navigate('DailyTask', { day });
  };

  const handleSettingsPress = () => {
    navigation.navigate('Settings');
  };

  const handlePomodoroPress = () => {
    navigation.navigate('Pomodoro');
  };

  if (state.loading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#2196F3" />
        <Text style={styles.loadingText}>Yükleniyor...</Text>
      </View>
    );
  }

  const currentDay = state.userProgress.active_day;
  const completedDays = state.userProgress.completed_days;
  const todayCompleted = state.userProgress.today_completed;

  return (
    <ScrollView style={styles.container}>
      {/* Header Section */}
      <View style={styles.header}>
        <Text style={styles.title}>14 Günde TYT Fiziği Bitir</Text>
        <Text style={styles.subtitle}>
          Aktif Gün: {currentDay} | Tamamlanan: {completedDays.length}/14
        </Text>
        {todayCompleted && (
          <Text style={styles.todayCompletedText}>
            ✅ Bugün bir gün tamamladın!
          </Text>
        )}
      </View>

      {/* Quick Actions */}
      <View style={styles.quickActions}>
        <TouchableOpacity style={styles.actionButton} onPress={handlePomodoroPress}>
          <Text style={styles.actionButtonText}>🍅 Pomodoro</Text>
        </TouchableOpacity>
        <TouchableOpacity style={styles.actionButton} onPress={handleSettingsPress}>
          <Text style={styles.actionButtonText}>⚙️ Ayarlar</Text>
        </TouchableOpacity>
      </View>

      {/* Current Day Card */}
      {currentDay <= 14 && (
        <View style={styles.currentDayCard}>
          <Text style={styles.currentDayTitle}>Bugünün Görevi</Text>
          <Text style={styles.currentDayTopic}>
            {physicsData.days[currentDay - 1]?.topic || 'Konu Yükleniyor...'}
          </Text>
          <Text style={styles.motivationText}>
            {physicsData.days[currentDay - 1]?.motivation || ''}
          </Text>
          <TouchableOpacity
            style={[
              styles.startButton,
              todayCompleted && styles.startButtonDisabled
            ]}
            onPress={() => handleDayPress(currentDay)}
            disabled={todayCompleted}
          >
            <Text style={styles.startButtonText}>
              {todayCompleted ? 'Bugün Tamamlandı' : 'Başla'}
            </Text>
          </TouchableOpacity>
        </View>
      )}

      {/* Days Grid */}
      <View style={styles.daysContainer}>
        <Text style={styles.sectionTitle}>Tüm Günler</Text>
        <View style={styles.daysGrid}>
          {physicsData.days.map((dayData, index) => {
            const dayNumber = index + 1;
            const isCompleted = completedDays.includes(dayNumber);
            const isAccessible = accessibleDays[index];
            const isCurrent = dayNumber === currentDay;

            return (
              <TouchableOpacity
                key={dayNumber}
                style={[
                  styles.dayCard,
                  isCompleted && styles.dayCardCompleted,
                  isCurrent && styles.dayCardCurrent,
                  !isAccessible && styles.dayCardLocked,
                ]}
                onPress={() => handleDayPress(dayNumber)}
                disabled={!isAccessible}
              >
                <Text style={[
                  styles.dayNumber,
                  isCompleted && styles.dayNumberCompleted,
                  !isAccessible && styles.dayNumberLocked,
                ]}>
                  {dayNumber}
                </Text>
                <Text style={[
                  styles.dayTopic,
                  isCompleted && styles.dayTopicCompleted,
                  !isAccessible && styles.dayTopicLocked,
                ]} numberOfLines={2}>
                  {dayData.topic}
                </Text>
                {isCompleted && <Text style={styles.completedIcon}>✅</Text>}
                {!isAccessible && <Text style={styles.lockedIcon}>🔒</Text>}
              </TouchableOpacity>
            );
          })}
        </View>
      </View>

      {/* Progress Summary */}
      <View style={styles.progressSummary}>
        <Text style={styles.sectionTitle}>İlerleme Özeti</Text>
        <View style={styles.progressBar}>
          <View
            style={[
              styles.progressFill,
              { width: `${(completedDays.length / 14) * 100}%` }
            ]}
          />
        </View>
        <Text style={styles.progressText}>
          {completedDays.length}/14 gün tamamlandı (%{Math.round((completedDays.length / 14) * 100)})
        </Text>
      </View>
    </ScrollView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#f5f5f5',
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#666',
  },
  header: {
    backgroundColor: '#2196F3',
    padding: 20,
    alignItems: 'center',
  },
  title: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#fff',
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 14,
    color: '#fff',
    marginTop: 5,
    opacity: 0.9,
  },
  todayCompletedText: {
    fontSize: 14,
    color: '#4CAF50',
    marginTop: 5,
    fontWeight: 'bold',
  },
  quickActions: {
    flexDirection: 'row',
    padding: 15,
    justifyContent: 'space-around',
  },
  actionButton: {
    backgroundColor: '#fff',
    paddingHorizontal: 20,
    paddingVertical: 10,
    borderRadius: 20,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  actionButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#333',
  },
  currentDayCard: {
    backgroundColor: '#fff',
    margin: 15,
    padding: 20,
    borderRadius: 10,
    elevation: 3,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  currentDayTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  currentDayTopic: {
    fontSize: 16,
    color: '#2196F3',
    fontWeight: '600',
    marginBottom: 10,
  },
  motivationText: {
    fontSize: 14,
    color: '#666',
    fontStyle: 'italic',
    marginBottom: 15,
    textAlign: 'center',
  },
  startButton: {
    backgroundColor: '#4CAF50',
    paddingVertical: 12,
    borderRadius: 8,
    alignItems: 'center',
  },
  startButtonDisabled: {
    backgroundColor: '#ccc',
  },
  startButtonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  daysContainer: {
    padding: 15,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 15,
  },
  daysGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  dayCard: {
    backgroundColor: '#fff',
    width: '48%',
    padding: 15,
    marginBottom: 10,
    borderRadius: 8,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    position: 'relative',
  },
  dayCardCompleted: {
    backgroundColor: '#E8F5E8',
    borderColor: '#4CAF50',
    borderWidth: 1,
  },
  dayCardCurrent: {
    backgroundColor: '#E3F2FD',
    borderColor: '#2196F3',
    borderWidth: 2,
  },
  dayCardLocked: {
    backgroundColor: '#f0f0f0',
    opacity: 0.6,
  },
  dayNumber: {
    fontSize: 18,
    fontWeight: 'bold',
    color: '#333',
    marginBottom: 5,
  },
  dayNumberCompleted: {
    color: '#4CAF50',
  },
  dayNumberLocked: {
    color: '#999',
  },
  dayTopic: {
    fontSize: 14,
    color: '#666',
    lineHeight: 18,
  },
  dayTopicCompleted: {
    color: '#4CAF50',
  },
  dayTopicLocked: {
    color: '#999',
  },
  completedIcon: {
    position: 'absolute',
    top: 5,
    right: 5,
    fontSize: 16,
  },
  lockedIcon: {
    position: 'absolute',
    top: 5,
    right: 5,
    fontSize: 16,
  },
  progressSummary: {
    padding: 15,
    backgroundColor: '#fff',
    margin: 15,
    borderRadius: 10,
    elevation: 2,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  progressBar: {
    height: 8,
    backgroundColor: '#e0e0e0',
    borderRadius: 4,
    marginVertical: 10,
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#4CAF50',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center',
  },
});
