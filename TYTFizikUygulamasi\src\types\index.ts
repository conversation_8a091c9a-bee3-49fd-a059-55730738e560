export interface Question {
  question: string;
  options: string[];
  correct_index: number;
}

export interface DayContent {
  day: number;
  topic: string;
  video_url: string;
  summary_text: string;
  external_task: string;
  motivation: string;
  test_questions: Question[];
}

export interface PhysicsData {
  days: DayContent[];
}

export interface UserProgress {
  active_day: number;
  completed_days: number[];
  last_login_date: string;
  today_completed: boolean;
  current_test_score?: number;
  test_attempts: { [day: number]: number };
}

export interface TestResult {
  score: number;
  correct_answers: number;
  total_questions: number;
  passed: boolean;
  wrong_questions: number[];
}

export interface PomodoroState {
  isRunning: boolean;
  currentPhase: 'work' | 'shortBreak' | 'longBreak';
  timeRemaining: number;
  cycleCount: number;
}

export type NavigationStackParamList = {
  Home: undefined;
  DailyTask: { day: number };
  Test: { day: number; questions: Question[] };
  TestResult: { result: TestResult; day: number };
  Pomodoro: undefined;
  Settings: undefined;
};

export type AppAction =
  | { type: 'SET_LOADING'; payload: boolean }
  | { type: 'SET_USER_PROGRESS'; payload: UserProgress }
  | { type: 'SET_TEST_RESULT'; payload: TestResult | null }
  | { type: 'COMPLETE_DAY'; payload: { day: number; score: number } }
  | { type: 'RESET_PROGRESS' };
