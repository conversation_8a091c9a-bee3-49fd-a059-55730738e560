import React from 'react';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { NavigationStackParamList } from '../types';

// Import screens (we'll create these next)
import HomeScreen from '../screens/HomeScreen';
import DailyTaskScreen from '../screens/DailyTaskScreen';
import TestScreen from '../screens/TestScreen';
import TestResultScreen from '../screens/TestResultScreen';
import PomodoroScreen from '../screens/PomodoroScreen';
import SettingsScreen from '../screens/SettingsScreen';

const Stack = createStackNavigator<NavigationStackParamList>();

export default function AppNavigator() {
  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName="Home"
        screenOptions={{
          headerStyle: {
            backgroundColor: '#2196F3',
          },
          headerTintColor: '#fff',
          headerTitleStyle: {
            fontWeight: 'bold',
            fontSize: 18,
          },
        }}
      >
        <Stack.Screen
          name="Home"
          component={HomeScreen}
          options={{
            title: '14 Günde TYT Fiziği Bitir',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="DailyTask"
          component={DailyTaskScreen}
          options={({ route }) => ({
            title: `${route.params.day}. Gün`,
            headerTitleAlign: 'center',
          })}
        />
        <Stack.Screen
          name="Test"
          component={TestScreen}
          options={{
            title: 'Test',
            headerTitleAlign: 'center',
            headerLeft: () => null, // Prevent going back during test
          }}
        />
        <Stack.Screen
          name="TestResult"
          component={TestResultScreen}
          options={{
            title: 'Test Sonucu',
            headerTitleAlign: 'center',
            headerLeft: () => null, // Prevent going back
          }}
        />
        <Stack.Screen
          name="Pomodoro"
          component={PomodoroScreen}
          options={{
            title: 'Pomodoro Timer',
            headerTitleAlign: 'center',
          }}
        />
        <Stack.Screen
          name="Settings"
          component={SettingsScreen}
          options={{
            title: 'Ayarlar',
            headerTitleAlign: 'center',
          }}
        />
      </Stack.Navigator>
    </NavigationContainer>
  );
}
